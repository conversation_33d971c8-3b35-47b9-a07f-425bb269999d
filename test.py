import nibabel as nib
import numpy as np

# Load the file
nii_img = nib.load("neuromed_demo_database/MY2023_089/MY2023_089_prediction.nii")

# Get the 3D/4D NumPy array
data = nii_img.get_fdata()   # float64 array by default
# OR: data = nii_img.get_fdata(dtype=np.float32)  # save memory

print(type(data))      # <class 'numpy.ndarray'>
print(data.shape)      # e.g. (256, 256, 150)
print(np.min(data), np.max(data))

# Get affine transformation (maps voxel coords → real-world coords)
print(nii_img.affine)

# Get header info
print(nii_img.header)
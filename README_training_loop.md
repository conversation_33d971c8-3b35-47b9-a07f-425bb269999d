# Multi-Field Annotator Predictor with Training Loop

This enhanced version of the MultiFieldAnnotatorPredictor includes intelligent training loop functionality that automatically handles retraining when new MRI scans or radiologists are added to your dataset.

## Key Features

### 🔄 Intelligent Retraining
- **Automatic Change Detection**: Detects when new examples or annotators are added
- **Smart Retraining Decisions**: Only retrains when changes exceed a configurable threshold
- **Model Persistence**: Saves and loads trained models to avoid unnecessary retraining

### 📊 Performance Tracking
- **Training History**: Tracks all training events with timestamps and metrics
- **Performance Monitoring**: Monitors consensus quality and annotator performance over time
- **Comprehensive Summaries**: Provides detailed performance summaries across all fields

### 🏥 Medical Imaging Workflow Support
- **New Scan Integration**: Easy addition of new MRI scans with automatic retraining
- **New Radiologist Integration**: Seamless addition of new radiologists to the annotation team
- **Field-Specific Models**: Separate models for different tumor characteristics (location, type, grade, etc.)

## Quick Start

### Basic Usage

```python
from multifieldannotator_predictor import MultiFieldAnnotatorPredictor

# Initialize with training loop capabilities
predictor = MultiFieldAnnotatorPredictor(
    verbose=True,
    model_save_dir="tumor_models",
    auto_retrain_threshold=0.1  # Retrain if data changes by >10%
)

# Initial training
fields = ["Tumor Location", "Tumor Type", "Tumor Grade"]
labels_dict = {...}  # Your annotator labels per field
features_dict = {...}  # Your MRI features per field

results = predictor.predict_for_fields(labels_dict, features_dict)
```

### Adding New MRI Scans

```python
# When you get a new MRI scan
new_scan_data = load_new_mri_scan("scan_123.nii")  # Your loading function
new_labels = get_radiologist_annotations("scan_123")  # Your annotation function

# Add to existing dataset - will automatically retrain if needed
update_info = predictor.add_new_scan(
    field="Tumor Location",
    new_labels_df=combined_labels_df,  # Old + new labels
    new_features=combined_features,    # Old + new features
    scan_id="MRI_SCAN_123"
)

if update_info['training_info']['reused_existing']:
    print("No retraining needed - changes were minimal")
else:
    print("Model retrained due to significant changes")
```

### Adding New Radiologists

```python
# When a new radiologist joins your team
new_radiologist_labels = get_new_radiologist_annotations()  # Your function

update_info = predictor.add_new_annotator(
    field="Tumor Type",
    updated_labels_df=labels_with_new_radiologist,
    features=existing_features,
    annotator_id="DR_SMITH"
)

# New annotators always trigger retraining
print(f"Added {update_info['training_info']['changes']['new_annotators']} new annotators")
```

## Configuration Options

### Constructor Parameters

```python
MultiFieldAnnotatorPredictor(
    model=None,                    # Base model (default: LogisticRegression)
    num_crossval_folds=5,         # Cross-validation folds
    verbose=False,                # Print detailed output
    model_save_dir="models",      # Directory to save models
    auto_retrain_threshold=0.1    # Threshold for automatic retraining
)
```

### Auto-Retrain Threshold

The `auto_retrain_threshold` controls when models are automatically retrained:

- `0.1` (10%): Retrain if dataset size changes by >10%
- `0.05` (5%): More sensitive - retrain with smaller changes
- `0.2` (20%): Less sensitive - only retrain with major changes

## Monitoring and Analysis

### Performance Summary

```python
# Get comprehensive performance metrics
performance_df = predictor.get_model_performance_summary()
print(performance_df)

# Output:
#           field  num_examples  avg_consensus_quality  ...
# 0  Tumor Location           120                  0.823  ...
# 1     Tumor Type           120                  0.756  ...
```

### Training History

```python
# View training history for all fields
history = predictor.get_training_history()

# Or for a specific field
location_history = predictor.get_training_history("Tumor Location")

for event in location_history["Tumor Location"]:
    print(f"Training: {event['timestamp']} - Quality: {event['avg_consensus_quality']:.3f}")
```

### Model Performance Analysis

```python
# Analyze worst performing annotators
for field, results in predictor.field_results.items():
    worst_annotators = results['annotator_stats'].head(3)
    print(f"\nWorst annotators for {field}:")
    print(worst_annotators[['annotator_quality', 'agreement_with_consensus']])
    
    # Find low quality examples that need review
    low_quality_mask = results['consensus_quality_scores'] < 0.5
    low_quality_examples = np.where(low_quality_mask)[0]
    print(f"Examples needing review: {len(low_quality_examples)}")
```

## File Structure

```
your_project/
├── multiannotator_predictor.py          # Base cleanlab wrapper
├── multifieldannotator_predictor.py     # Enhanced multi-field predictor
├── test_training_loop.py                # Test suite
├── tumor_models/                        # Saved models directory
│   ├── tumor_location_model.pkl
│   ├── tumor_type_model.pkl
│   └── tumor_grade_model.pkl
└── README_training_loop.md              # This file
```

## Integration with Your MRI Pipeline

### Typical Workflow

1. **Initial Setup**: Train models on your existing annotated MRI dataset
2. **New Scan Processing**: 
   - Extract features from new MRI scan
   - Get radiologist annotations
   - Add to dataset using `add_new_scan()`
3. **New Radiologist Onboarding**:
   - Get annotations from new radiologist on existing scans
   - Add using `add_new_annotator()`
4. **Monitoring**: Regularly check performance summaries and training history

### Example Integration

```python
class MRIPipeline:
    def __init__(self):
        self.predictor = MultiFieldAnnotatorPredictor(
            model_save_dir="production_models",
            auto_retrain_threshold=0.05,  # Sensitive to changes
            verbose=True
        )
    
    def process_new_scan(self, scan_path):
        # Your feature extraction
        features = extract_mri_features(scan_path)
        
        # Get annotations from all radiologists
        labels = collect_annotations(scan_path)
        
        # Update models
        for field in ["Location", "Type", "Grade"]:
            self.predictor.add_new_scan(
                field=field,
                new_labels_df=labels[field],
                new_features=features,
                scan_id=os.path.basename(scan_path)
            )
    
    def onboard_radiologist(self, radiologist_id):
        # Get annotations from new radiologist on existing scans
        new_annotations = get_radiologist_annotations(radiologist_id)
        
        for field, labels in new_annotations.items():
            self.predictor.add_new_annotator(
                field=field,
                updated_labels_df=labels,
                features=self.existing_features[field],
                annotator_id=radiologist_id
            )
```

## Best Practices

1. **Regular Monitoring**: Check performance summaries weekly
2. **Threshold Tuning**: Adjust `auto_retrain_threshold` based on your data characteristics
3. **Model Versioning**: Consider implementing model versioning for production use
4. **Quality Control**: Review low-quality examples identified by the system
5. **Backup Models**: Keep backups of well-performing models

## Troubleshooting

### Common Issues

1. **Models not retraining**: Check if changes exceed the threshold
2. **Memory issues**: Reduce `num_crossval_folds` for large datasets
3. **Slow training**: Consider using faster models or reducing cross-validation folds

### Performance Optimization

- Use `force_retrain=False` to let the system decide when to retrain
- Set appropriate thresholds based on your data update frequency
- Monitor training times and adjust model complexity accordingly

## Requirements

- cleanlab
- scikit-learn
- pandas
- numpy
- pickle (built-in)

Install with:
```bash
pip install cleanlab scikit-learn pandas numpy
```

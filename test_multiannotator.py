#!/usr/bin/env python3
"""
Test script for the MultiAnnotatorPredictor class.
"""

import numpy as np
import pandas as pd
from multiannotator_predictor import MultiAnnotatorPredictor, create_example_data


def test_basic_functionality():
    """Test basic functionality of the MultiAnnotatorPredictor."""
    print("Testing MultiAnnotatorPredictor basic functionality...")
    
    # Create small example data for testing
    data = create_example_data(num_examples=50, num_annotators=10, seed=123)
    
    # Initialize predictor
    predictor = MultiAnnotatorPredictor(verbose=False)
    
    # Test prediction
    results = predictor.predict(
        multiannotator_labels=data['multiannotator_labels'],
        features=data['features']
    )
    
    # Verify results structure
    expected_keys = ['consensus_labels', 'consensus_quality_scores', 'label_quality', 
                     'annotator_stats', 'detailed_label_quality', 'majority_vote_labels', 'pred_probs']
    
    for key in expected_keys:
        assert key in results, f"Missing key: {key}"
    
    # Verify shapes
    n_examples = data['features'].shape[0]
    n_annotators = data['multiannotator_labels'].shape[1]
    
    assert len(results['consensus_labels']) == n_examples, "Consensus labels length mismatch"
    assert len(results['consensus_quality_scores']) == n_examples, "Quality scores length mismatch"
    assert len(results['label_quality']) == n_examples, "Label quality DataFrame length mismatch"
    assert len(results['annotator_stats']) == n_annotators, "Annotator stats length mismatch"
    
    print("✓ Basic functionality test passed")


def test_utility_methods():
    """Test utility methods of the MultiAnnotatorPredictor."""
    print("Testing utility methods...")
    
    # Create example data
    data = create_example_data(num_examples=30, num_annotators=8, seed=456)
    
    # Initialize predictor and run prediction
    predictor = MultiAnnotatorPredictor(verbose=False)
    results = predictor.predict(
        multiannotator_labels=data['multiannotator_labels'],
        features=data['features']
    )
    
    # Test summary stats
    stats = predictor.get_summary_stats()
    assert stats is not None, "Summary stats should not be None"
    assert 'num_examples' in stats, "Missing num_examples in stats"
    assert 'avg_consensus_quality' in stats, "Missing avg_consensus_quality in stats"
    
    # Test worst annotators
    worst = predictor.get_worst_annotators(top_k=3)
    assert worst is not None, "Worst annotators should not be None"
    assert len(worst) <= 3, "Should return at most 3 annotators"
    
    # Test best annotators
    best = predictor.get_best_annotators(top_k=3)
    assert best is not None, "Best annotators should not be None"
    assert len(best) <= 3, "Should return at most 3 annotators"
    
    # Test low quality examples
    low_quality = predictor.get_low_quality_examples(threshold=0.8, top_k=5)
    assert low_quality is not None, "Low quality examples should not be None"
    
    print("✓ Utility methods test passed")


def test_with_custom_model():
    """Test with a custom model."""
    print("Testing with custom model...")
    
    from sklearn.ensemble import RandomForestClassifier
    
    # Create example data
    data = create_example_data(num_examples=40, num_annotators=6, seed=789)
    
    # Initialize predictor with custom model
    custom_model = RandomForestClassifier(n_estimators=10, random_state=42)
    predictor = MultiAnnotatorPredictor(model=custom_model, verbose=False)
    
    # Test prediction
    results = predictor.predict(
        multiannotator_labels=data['multiannotator_labels'],
        features=data['features']
    )
    
    assert 'consensus_labels' in results, "Missing consensus labels"
    assert len(results['consensus_labels']) == data['features'].shape[0], "Length mismatch"
    
    print("✓ Custom model test passed")


def test_error_handling():
    """Test error handling."""
    print("Testing error handling...")
    
    # Create example data
    data = create_example_data(num_examples=20, num_annotators=5, seed=999)
    
    predictor = MultiAnnotatorPredictor(verbose=False)
    
    # Test mismatched dimensions
    try:
        wrong_features = np.random.randn(10, 2)  # Wrong number of examples
        predictor.predict(
            multiannotator_labels=data['multiannotator_labels'],
            features=wrong_features
        )
        assert False, "Should have raised ValueError for dimension mismatch"
    except ValueError:
        pass  # Expected
    
    # Test utility methods before prediction
    stats = predictor.get_summary_stats()
    assert stats is None, "Should return None before prediction"
    
    worst = predictor.get_worst_annotators()
    assert worst is None, "Should return None before prediction"
    
    print("✓ Error handling test passed")


def run_all_tests():
    """Run all tests."""
    print("Running MultiAnnotatorPredictor tests...\n")
    
    try:
        test_basic_functionality()
        test_utility_methods()
        test_with_custom_model()
        test_error_handling()
        
        print("\n✅ All tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)

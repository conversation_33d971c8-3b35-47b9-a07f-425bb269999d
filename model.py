import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset

# ---------------------------
# 1. Dataset
# ---------------------------
class MRIDataset(Dataset):
    def __init__(self, features, annotator_ids, labels, annotator_to_idx):
        self.features = torch.tensor(features, dtype=torch.float32)
        self.annotators = [annotator_to_idx[a] for a in annotator_ids]
        self.annotators = torch.tensor(self.annotators, dtype=torch.long)
        self.labels = torch.tensor(labels, dtype=torch.float32)

    def __len__(self):
        return len(self.features)

    def __getitem__(self, idx):
        return self.features[idx], self.annotators[idx], self.labels[idx]


# ---------------------------
# 2. Model
# ---------------------------
class MultiAnnotatorPredictor(nn.Module):
    def __init__(self, feature_dim, num_annotators):
        super().__init__()
        self.feature_extractor = nn.Sequential(
            nn.Linear(feature_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU()
        )
        # Annotator embeddings to capture bias
        self.annotator_embedding = nn.Embedding(num_annotators, 8)

        # Combine features + annotator bias
        self.classifier = nn.Sequential(
            nn.Linear(64 + 8, 1),
            nn.Sigmoid()
        )

    def forward(self, x, annotator_ids):
        feat = self.feature_extractor(x)
        annot_bias = self.annotator_embedding(annotator_ids)
        combined = torch.cat([feat, annot_bias], dim=1)
        return self.classifier(combined).squeeze()


# ---------------------------
# 3. Training Loop
# ---------------------------
def train_model(model, dataloader, epochs=5, lr=1e-3):
    optimizer = optim.Adam(model.parameters(), lr=lr)
    criterion = nn.BCELoss()

    for epoch in range(epochs):
        total_loss = 0
        for x, annotator_ids, y in dataloader:
            optimizer.zero_grad()
            preds = model(x, annotator_ids)
            loss = criterion(preds, y)
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        print(f"Epoch {epoch+1}/{epochs}, Loss: {total_loss:.4f}")
    return model


# ---------------------------
# 4. Save / Load Functions
# ---------------------------
def save_checkpoint(model, annotator_to_idx, path="multiannotator.pt"):
    torch.save({
        "model_state": model.state_dict(),
        "annotator_to_idx": annotator_to_idx
    }, path)

def load_checkpoint(path, feature_dim):
    ckpt = torch.load(path)
    model = MultiAnnotatorPredictor(feature_dim, len(ckpt["annotator_to_idx"]))
    model.load_state_dict(ckpt["model_state"])
    return model, ckpt["annotator_to_idx"]


# ---------------------------
# 5. Incremental Update Logic
# ---------------------------
def incremental_update(existing_model, annotator_to_idx, new_data, feature_dim):
    features, annotators, labels = new_data

    # Detect new annotators
    new_annotators = set(annotators) - set(annotator_to_idx.keys())
    if new_annotators:
        print(f"Adding new annotators: {new_annotators}")
        # Expand embedding layer for new annotators
        old_weight = existing_model.annotator_embedding.weight.data
        new_embed = nn.Embedding(len(annotator_to_idx) + len(new_annotators), old_weight.shape[1])
        new_embed.weight.data[:len(annotator_to_idx)] = old_weight
        nn.init.normal_(new_embed.weight.data[len(annotator_to_idx):], mean=0, std=0.02)
        existing_model.annotator_embedding = new_embed

        # Update mapping
        for na in new_annotators:
            annotator_to_idx[na] = len(annotator_to_idx)

    # Create DataLoader with new + some old data (you’d merge here in practice)
    dataset = MRIDataset(features, annotators, labels, annotator_to_idx)
    dataloader = DataLoader(dataset, batch_size=16, shuffle=True)

    # Fine-tune model
    train_model(existing_model, dataloader, epochs=3, lr=1e-4)

    return existing_model, annotator_to_idx


# ---------------------------
# Example Usage
# ---------------------------
if __name__ == "__main__":
    # Example: MRI feature vectors (pretend extracted from U-Net encoder)
    feature_dim = 10
    features = [[0.1]*10, [0.2]*10, [0.3]*10]
    annotators = ["dr_a", "dr_b", "dr_a"]
    labels = [1, 0, 1]

    # Initial mapping
    annotator_to_idx = {a: i for i, a in enumerate(sorted(set(annotators)))}

    # Initial training
    dataset = MRIDataset(features, annotators, labels, annotator_to_idx)
    dataloader = DataLoader(dataset, batch_size=2, shuffle=True)
    model = MultiAnnotatorPredictor(feature_dim, len(annotator_to_idx))
    model = train_model(model, dataloader, epochs=5)

    # Save
    save_checkpoint(model, annotator_to_idx)

    # Later: Load and update with new doctor + new cases
    model, annotator_to_idx = load_checkpoint("multiannotator.pt", feature_dim)
    new_features = [[0.5]*10, [0.6]*10]
    new_annotators = ["dr_c", "dr_a"]  # dr_c is new
    new_labels = [0, 1]
    model, annotator_to_idx = incremental_update(model, annotator_to_idx,
                                                 (new_features, new_annotators, new_labels),
                                                 feature_dim)

    # Save updated model
    save_checkpoint(model, annotator_to_idx, "multiannotator_updated.pt")

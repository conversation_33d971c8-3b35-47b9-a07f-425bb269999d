from multiannotator_predictor import MultiAnnotatorPredictor, create_example_data

class MultiFieldAnnotatorPredictor:
    """
    Wrapper to run MultiAnnotatorPredictor on multiple tumor-related fields,
    skipping Tumor Presence (assumed always 'Yes').
    """
    def __init__(self, model=None, num_crossval_folds: int = 5, verbose: bool = False):
        self.model = model
        self.num_crossval_folds = num_crossval_folds
        self.verbose = verbose
        self.field_results = {}

    def predict_for_fields(
        self,
        multiannotator_labels_dict: Dict[str, pd.DataFrame],
        features_dict: Dict[str, np.ndarray]
    ) -> Dict[str, Dict[str, Any]]:
        """
        Runs consensus prediction for each tumor metadata field (except presence).
        
        Args:
            multiannotator_labels_dict: dict mapping field name -> annotator label DataFrame
            features_dict: dict mapping field name -> feature matrix for that field
        
        Returns:
            dict: field name -> results from MultiAnnotatorPredictor.predict()
        """
        for field, labels_df in multiannotator_labels_dict.items():
            if field.lower() in ["tumor presence", "presence"]:
                if self.verbose:
                    print(f"Skipping field '{field}' (always positive).")
                continue
            
            if self.verbose:
                print(f"\nProcessing field: {field}")

            predictor = MultiAnnotatorPredictor(
                model=self.model,
                num_crossval_folds=self.num_crossval_folds,
                verbose=self.verbose
            )

            results = predictor.predict(
                multiannotator_labels=labels_df,
                features=features_dict[field],
                return_detailed_results=True
            )
            self.field_results[field] = results

        return self.field_results
        
        
if __name__ == "__main__":
    # Example synthetic data for each field (replace with your actual MRI-derived feature sets)
    fields = ["Tumor Location", "Tumor Type", "Tumor Grade", "Size / Volume Category"]

    labels_dict = {}
    features_dict = {}
    for f in fields:
        data = create_example_data(num_examples=100, num_annotators=10, num_classes=3, seed=42)
        labels_dict[f] = data['multiannotator_labels']
        features_dict[f] = data['features']

    multi_field_predictor = MultiFieldAnnotatorPredictor(verbose=True)
    all_results = multi_field_predictor.predict_for_fields(labels_dict, features_dict)

    # Access consensus results per field
    for field, res in all_results.items():
        print(f"\n=== {field} ===")
        print(res['label_quality'].head())

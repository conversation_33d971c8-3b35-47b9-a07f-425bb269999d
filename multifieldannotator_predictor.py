import numpy as np
import pandas as pd
import pickle
import os
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_predict
from sklearn.metrics import accuracy_score, classification_report
import warnings

from multiannotator_predictor import MultiAnnotatorPredictor, create_example_data

class MultiFieldAnnotatorPredictor:
    """
    Enhanced wrapper to run MultiAnnotatorPredictor on multiple tumor-related fields
    with training loop support for incremental learning when new MRI scans or radiologists are added.
    """
    def __init__(
        self,
        model=None,
        num_crossval_folds: int = 5,
        verbose: bool = False,
        model_save_dir: str = "models",
        auto_retrain_threshold: float = 0.1
    ):
        """
        Initialize the MultiFieldAnnotatorPredictor with training loop capabilities.

        Args:
            model: Base model to use (defaults to LogisticRegression)
            num_crossval_folds: Number of CV folds for out-of-sample predictions
            verbose: Whether to print verbose output
            model_save_dir: Directory to save trained models
            auto_retrain_threshold: Threshold for automatic retraining when data changes significantly
        """
        self.base_model = model if model is not None else LogisticRegression(random_state=42)
        self.num_crossval_folds = num_crossval_folds
        self.verbose = verbose
        self.model_save_dir = model_save_dir
        self.auto_retrain_threshold = auto_retrain_threshold

        # Training state
        self.field_results = {}
        self.field_models = {}  # Store trained models per field
        self.field_data_history = {}  # Track data changes per field
        self.training_history = {}  # Track training metrics over time

        # Create model directory if it doesn't exist
        os.makedirs(model_save_dir, exist_ok=True)

    def _detect_data_changes(self, field: str, labels_df: pd.DataFrame, features: np.ndarray) -> Dict[str, Any]:
        """
        Detect changes in data that might require retraining.

        Args:
            field: Field name
            labels_df: Current annotator labels DataFrame
            features: Current feature matrix

        Returns:
            Dictionary with change statistics
        """
        changes = {
            'new_examples': 0,
            'new_annotators': 0,
            'data_size_change_ratio': 0.0,
            'requires_retraining': False
        }

        if field not in self.field_data_history:
            # First time seeing this field
            changes['new_examples'] = len(labels_df)
            changes['new_annotators'] = len(labels_df.columns)
            changes['requires_retraining'] = True
        else:
            prev_data = self.field_data_history[field]

            # Check for new examples
            changes['new_examples'] = len(labels_df) - prev_data['num_examples']

            # Check for new annotators
            prev_annotators = set(prev_data['annotator_columns'])
            current_annotators = set(labels_df.columns)
            changes['new_annotators'] = len(current_annotators - prev_annotators)

            # Calculate data size change ratio
            if prev_data['num_examples'] > 0:
                changes['data_size_change_ratio'] = changes['new_examples'] / prev_data['num_examples']

            # Determine if retraining is needed
            changes['requires_retraining'] = (
                changes['new_annotators'] > 0 or
                abs(changes['data_size_change_ratio']) > self.auto_retrain_threshold
            )

        return changes

    def _update_data_history(self, field: str, labels_df: pd.DataFrame, features: np.ndarray):
        """Update the data history for a field."""
        self.field_data_history[field] = {
            'num_examples': len(labels_df),
            'num_features': features.shape[1],
            'annotator_columns': list(labels_df.columns),
            'last_updated': datetime.now()
        }

    def _save_field_model(self, field: str, predictor: MultiAnnotatorPredictor):
        """Save a trained model for a specific field."""
        model_path = os.path.join(self.model_save_dir, f"{field.replace(' ', '_').lower()}_model.pkl")

        model_data = {
            'predictor': predictor,
            'training_timestamp': datetime.now(),
            'data_info': self.field_data_history.get(field, {})
        }

        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)

        if self.verbose:
            print(f"Saved model for field '{field}' to {model_path}")

    def _load_field_model(self, field: str) -> Optional[MultiAnnotatorPredictor]:
        """Load a previously trained model for a specific field."""
        model_path = os.path.join(self.model_save_dir, f"{field.replace(' ', '_').lower()}_model.pkl")

        if not os.path.exists(model_path):
            return None

        try:
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)

            if self.verbose:
                print(f"Loaded existing model for field '{field}' from {model_path}")

            return model_data['predictor']
        except Exception as e:
            if self.verbose:
                print(f"Failed to load model for field '{field}': {e}")
            return None

    def train_field_model(
        self,
        field: str,
        labels_df: pd.DataFrame,
        features: np.ndarray,
        force_retrain: bool = False
    ) -> Tuple[MultiAnnotatorPredictor, Dict[str, Any]]:
        """
        Train or retrain a model for a specific field with change detection.

        Args:
            field: Field name
            labels_df: Annotator labels DataFrame
            features: Feature matrix
            force_retrain: Force retraining even if not needed

        Returns:
            Tuple of (trained_predictor, training_info)
        """
        if self.verbose:
            print(f"\n=== Training model for field: {field} ===")

        # Detect data changes
        changes = self._detect_data_changes(field, labels_df, features)

        if self.verbose:
            print(f"Data changes detected:")
            print(f"  - New examples: {changes['new_examples']}")
            print(f"  - New annotators: {changes['new_annotators']}")
            print(f"  - Data size change ratio: {changes['data_size_change_ratio']:.3f}")
            print(f"  - Requires retraining: {changes['requires_retraining']}")

        # Check if we need to retrain
        if not force_retrain and not changes['requires_retraining']:
            # Try to load existing model
            existing_predictor = self._load_field_model(field)
            if existing_predictor is not None:
                if self.verbose:
                    print("Using existing model (no significant changes detected)")
                return existing_predictor, {'reused_existing': True, 'changes': changes}

        # Train new model
        if self.verbose:
            print("Training new model...")

        predictor = MultiAnnotatorPredictor(
            model=self.base_model,
            num_crossval_folds=self.num_crossval_folds,
            verbose=self.verbose
        )

        # Train the model
        start_time = datetime.now()
        results = predictor.predict(
            multiannotator_labels=labels_df,
            features=features,
            return_detailed_results=True
        )
        training_time = (datetime.now() - start_time).total_seconds()

        # Store results and model
        self.field_results[field] = results
        self.field_models[field] = predictor

        # Update data history
        self._update_data_history(field, labels_df, features)

        # Save model
        self._save_field_model(field, predictor)

        # Record training metrics
        training_info = {
            'reused_existing': False,
            'changes': changes,
            'training_time_seconds': training_time,
            'num_examples': len(labels_df),
            'num_annotators': len(labels_df.columns),
            'avg_consensus_quality': results['consensus_quality_scores'].mean(),
            'timestamp': datetime.now()
        }

        # Update training history
        if field not in self.training_history:
            self.training_history[field] = []
        self.training_history[field].append(training_info)

        if self.verbose:
            print(f"Training completed in {training_time:.2f} seconds")
            print(f"Average consensus quality: {training_info['avg_consensus_quality']:.3f}")

        return predictor, training_info

    def predict_for_fields(
        self,
        multiannotator_labels_dict: Dict[str, pd.DataFrame],
        features_dict: Dict[str, np.ndarray],
        force_retrain: bool = False
    ) -> Dict[str, Dict[str, Any]]:
        """
        Runs consensus prediction for each tumor metadata field with intelligent retraining.

        Args:
            multiannotator_labels_dict: dict mapping field name -> annotator label DataFrame
            features_dict: dict mapping field name -> feature matrix for that field
            force_retrain: Force retraining of all models regardless of changes

        Returns:
            dict: field name -> results from MultiAnnotatorPredictor.predict()
        """
        training_summary = {}

        for field, labels_df in multiannotator_labels_dict.items():
            if field.lower() in ["tumor presence", "presence"]:
                if self.verbose:
                    print(f"Skipping field '{field}' (always positive).")
                continue

            # Train or load model for this field
            predictor, training_info = self.train_field_model(
                field=field,
                labels_df=labels_df,
                features=features_dict[field],
                force_retrain=force_retrain
            )

            training_summary[field] = training_info

        if self.verbose:
            self._print_training_summary(training_summary)

        return self.field_results

    def _print_training_summary(self, training_summary: Dict[str, Dict[str, Any]]):
        """Print a summary of training results."""
        print("\n" + "="*60)
        print("TRAINING SUMMARY")
        print("="*60)

        total_training_time = 0
        reused_count = 0
        retrained_count = 0

        for field, info in training_summary.items():
            status = "REUSED" if info['reused_existing'] else "RETRAINED"
            print(f"{field:30} | {status:10} | ", end="")

            if info['reused_existing']:
                reused_count += 1
                print("No significant changes")
            else:
                retrained_count += 1
                total_training_time += info['training_time_seconds']
                print(f"Time: {info['training_time_seconds']:.2f}s | "
                      f"Quality: {info['avg_consensus_quality']:.3f} | "
                      f"Examples: {info['num_examples']} | "
                      f"Annotators: {info['num_annotators']}")

        print("-" * 60)
        print(f"Total fields processed: {len(training_summary)}")
        print(f"Models reused: {reused_count}")
        print(f"Models retrained: {retrained_count}")
        if retrained_count > 0:
            print(f"Total training time: {total_training_time:.2f}s")
            print(f"Average training time: {total_training_time/retrained_count:.2f}s")
        print("="*60)

    def add_new_scan(
        self,
        field: str,
        new_labels_df: pd.DataFrame,
        new_features: np.ndarray,
        scan_id: str = None
    ) -> Dict[str, Any]:
        """
        Add a new MRI scan to the dataset and retrain if necessary.

        Args:
            field: Field name to update
            new_labels_df: New annotator labels (can include existing + new data)
            new_features: New feature matrix (can include existing + new data)
            scan_id: Optional identifier for the new scan

        Returns:
            Dictionary with update information
        """
        if self.verbose:
            print(f"\nAdding new scan data for field: {field}")
            if scan_id:
                print(f"Scan ID: {scan_id}")

        # Train model with new data
        predictor, training_info = self.train_field_model(
            field=field,
            labels_df=new_labels_df,
            features=new_features,
            force_retrain=False  # Let the system decide based on changes
        )

        update_info = {
            'scan_id': scan_id,
            'field': field,
            'training_info': training_info,
            'timestamp': datetime.now()
        }

        if self.verbose:
            if training_info['reused_existing']:
                print("No retraining needed - changes were minimal")
            else:
                print(f"Model retrained due to significant changes")
                print(f"New examples: {training_info['changes']['new_examples']}")

        return update_info

    def add_new_annotator(
        self,
        field: str,
        updated_labels_df: pd.DataFrame,
        features: np.ndarray,
        annotator_id: str = None
    ) -> Dict[str, Any]:
        """
        Add a new annotator (radiologist) to the dataset and retrain if necessary.

        Args:
            field: Field name to update
            updated_labels_df: Updated labels DataFrame including new annotator
            features: Feature matrix (should match existing data)
            annotator_id: Optional identifier for the new annotator

        Returns:
            Dictionary with update information
        """
        if self.verbose:
            print(f"\nAdding new annotator for field: {field}")
            if annotator_id:
                print(f"Annotator ID: {annotator_id}")

        # Train model with new annotator data
        predictor, training_info = self.train_field_model(
            field=field,
            labels_df=updated_labels_df,
            features=features,
            force_retrain=True  # New annotator always requires retraining
        )

        update_info = {
            'annotator_id': annotator_id,
            'field': field,
            'training_info': training_info,
            'timestamp': datetime.now()
        }

        if self.verbose:
            print(f"Model retrained with new annotator")
            print(f"New annotators: {training_info['changes']['new_annotators']}")

        return update_info

    def get_training_history(self, field: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get training history for all fields or a specific field.

        Args:
            field: Optional field name to get history for

        Returns:
            Training history dictionary
        """
        if field is not None:
            return {field: self.training_history.get(field, [])}
        return self.training_history.copy()

    def get_model_performance_summary(self) -> pd.DataFrame:
        """
        Get a summary of model performance across all fields.

        Returns:
            DataFrame with performance metrics per field
        """
        summary_data = []

        for field, results in self.field_results.items():
            if results is None:
                continue

            # Get latest training info
            history = self.training_history.get(field, [])
            latest_training = history[-1] if history else {}

            summary_data.append({
                'field': field,
                'num_examples': len(results['consensus_labels']),
                'num_annotators': len(results['annotator_stats']),
                'avg_consensus_quality': results['consensus_quality_scores'].mean(),
                'min_consensus_quality': results['consensus_quality_scores'].min(),
                'max_consensus_quality': results['consensus_quality_scores'].max(),
                'avg_annotator_agreement': results['label_quality']['annotator_agreement'].mean(),
                'worst_annotator_quality': results['annotator_stats']['annotator_quality'].min(),
                'best_annotator_quality': results['annotator_stats']['annotator_quality'].max(),
                'last_training_time': latest_training.get('training_time_seconds', 0),
                'last_updated': latest_training.get('timestamp', 'Never')
            })

        return pd.DataFrame(summary_data)

    def cleanup_old_models(self, keep_latest: int = 3):
        """
        Clean up old model files, keeping only the latest versions.

        Args:
            keep_latest: Number of latest models to keep per field
        """
        if self.verbose:
            print(f"Cleaning up old models, keeping latest {keep_latest} per field...")

        # This is a placeholder - in a real implementation, you'd want to
        # implement versioned model storage and cleanup logic
        warnings.warn("Model cleanup not fully implemented - implement versioned storage for production use")
        
        
if __name__ == "__main__":
    # Example usage demonstrating the training loop functionality
    print("=== Multi-Field Annotator Predictor with Training Loop ===\n")

    # Initialize predictor with training loop capabilities
    multi_field_predictor = MultiFieldAnnotatorPredictor(
        verbose=True,
        model_save_dir="tumor_models",
        auto_retrain_threshold=0.1
    )

    # Example synthetic data for each field (replace with your actual MRI-derived feature sets)
    fields = ["Tumor Location", "Tumor Type", "Tumor Grade", "Size / Volume Category"]

    print("1. Initial training with baseline data...")
    labels_dict = {}
    features_dict = {}
    for f in fields:
        data = create_example_data(num_examples=100, num_annotators=8, num_classes=3, seed=42)
        labels_dict[f] = data['multiannotator_labels']
        features_dict[f] = data['features']

    # Initial training
    all_results = multi_field_predictor.predict_for_fields(labels_dict, features_dict)

    print("\n2. Simulating addition of new MRI scan...")
    # Simulate adding a new MRI scan (more examples)
    new_scan_data = create_example_data(num_examples=120, num_annotators=8, num_classes=3, seed=123)
    update_info = multi_field_predictor.add_new_scan(
        field="Tumor Location",
        new_labels_df=new_scan_data['multiannotator_labels'],
        new_features=new_scan_data['features'],
        scan_id="MRI_SCAN_001"
    )

    print("\n3. Simulating addition of new radiologist...")
    # Simulate adding a new radiologist (more annotators)
    new_radiologist_data = create_example_data(num_examples=100, num_annotators=10, num_classes=3, seed=456)
    update_info = multi_field_predictor.add_new_annotator(
        field="Tumor Type",
        updated_labels_df=new_radiologist_data['multiannotator_labels'],
        features=new_radiologist_data['features'],
        annotator_id="DR_SMITH"
    )

    print("\n4. Performance summary across all fields:")
    performance_df = multi_field_predictor.get_model_performance_summary()
    print(performance_df.round(3))

    print("\n5. Training history for Tumor Location:")
    history = multi_field_predictor.get_training_history("Tumor Location")
    for i, training_event in enumerate(history["Tumor Location"]):
        print(f"  Training {i+1}: {training_event['timestamp'].strftime('%Y-%m-%d %H:%M:%S')} - "
              f"Quality: {training_event['avg_consensus_quality']:.3f}")

    print("\n6. Final results for each field:")
    for field, res in multi_field_predictor.field_results.items():
        print(f"\n=== {field} ===")
        print(f"Consensus labels: {len(res['consensus_labels'])} examples")
        print(f"Average quality: {res['consensus_quality_scores'].mean():.3f}")
        print(f"Number of annotators: {len(res['annotator_stats'])}")

        # Show top 3 examples with lowest quality
        low_quality_idx = np.argsort(res['consensus_quality_scores'])[:3]
        print("Lowest quality examples:")
        for idx in low_quality_idx:
            print(f"  Example {idx}: Quality = {res['consensus_quality_scores'][idx]:.3f}, "
                  f"Label = {res['consensus_labels'][idx]}")

    print("\n=== Training Loop Demo Complete ===")
    print("Key features demonstrated:")
    print("- Automatic change detection")
    print("- Intelligent retraining decisions")
    print("- Model persistence and loading")
    print("- Performance tracking over time")
    print("- Easy addition of new scans and annotators")

import os
import numpy as np
import pinecone
import uuid
import json
from openai import OpenAI
from cleanlab import CrowdlabClient  # placeholder for actual SDK

# 🔧 Configs
PINECONE_API_KEY = "pcsk_6K4z5X_7EtrVfCTASwXGxEXqb5U8ztagsyhN28cDe7q3xRcgwYWZUnqC9baUz8zUgciFTR"
PINECONE_ENV = 'your-pinecone-env'
INDEX_NAME = 'mri-cases'
LLM_API_KEY = os.getenv("OPENAI_API_KEY")
crowdlab = CrowdlabClient(api_key="your-crowdlab-api-key")

openai = OpenAI(api_key=LLM_API_KEY)

# 🔁 1. Connect to Pinecone
pinecone.init(api_key=PINECONE_API_KEY, environment=PINECONE_ENV)
index = pinecone.Index(INDEX_NAME)

# 🧠 2. Feature extraction from model output
def extract_embedding_from_mask(mask_3d):
    """Convert 3D segmentation to a fixed-length embedding."""
    flat = mask_3d.flatten()
    hist, _ = np.histogram(flat, bins=[0,1,2,3,4])  # assuming 0-3 classes
    hist = hist / np.sum(hist)
    return hist.tolist()

# 🧮 3. Inference result -> Pinecone query
def query_similar_cases(mask_3d, top_k=5):
    embedding = extract_embedding_from_mask(mask_3d)
    results = index.query(vector=embedding, top_k=top_k, include_metadata=True)
    return results

# 🗃️ 4. Process matches
def retrieve_comments_and_labels(results):
    past_comments = []
    past_labels = []
    for match in results.matches:
        meta = match.metadata
        past_comments.append(meta.get('comment', ''))
        past_labels.append(meta.get('label', ''))
    return past_labels, past_comments

# 👨‍⚕️ 5. Crowdsourcing aggregation
def aggregate_labels_with_crowdlab(image_id, candidate_labels):
    job = crowdlab.create_label_job(
        task_type="classification",
        image_id=image_id,
        choices=list(set(candidate_labels)),
        existing_labels=candidate_labels,
    )
    return job.get_aggregated_label()

# 🧠 6. LLM summary generation
def generate_summary(current_comment, past_comments):
    prompt = f"""
You are a medical AI assistant. Given the current doctor's comment and past historical comments, generate a concise summary.

Current comment:
{current_comment}

Historical comments:
{json.dumps(past_comments, indent=2)}

Return a summary for the report:
"""
    response = openai.chat.completions.create(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content.strip()

# 📄 7. Final report
def generate_report(image_id, mask_3d, current_comment):
    embedding = extract_embedding_from_mask(mask_3d)
    case_id = str(uuid.uuid4())
    
    # Step 2: query similar cases
    results = query_similar_cases(mask_3d)
    past_labels, past_comments = retrieve_comments_and_labels(results)

    # Step 3: aggregate label
    final_label = aggregate_labels_with_crowdlab(image_id, past_labels)

    # Step 4: generate summary
    summary = generate_summary(current_comment, past_comments)

    # Step 5: upsert this case to Pinecone
    index.upsert([
        (case_id, embedding, {
            "label": final_label,
            "comment": current_comment
        })
    ])

    # Step 6: return report
    report = {
        "image_id": image_id,
        "label": final_label,
        "comment_summary": summary
    }
    return report
